import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Observable, timer, BehaviorSubject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { NotificationService } from './notification.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ShareDataService } from './share-data.service';

@Injectable({
  providedIn: 'root'
})
export class TimeOutService {

  private userActivity$ = new Subject<void>();
  private destroyed$ = new Subject<void>();
  private timeoutInSeconds = 18000; // 30 minutes
  private lastAction: number;
  private logoutTimer$ = new BehaviorSubject<number>(this.timeoutInSeconds);
  constructor(private router: Router ,private notify: NotificationService,private snackBar: MatSnackBar, private sharedData: ShareDataService,) {
    this.lastAction = Date.now();
    this.initListener();


  }

  private initListener(): void {
    document.addEventListener('mousemove', () => this.resetTimer());
    document.addEventListener('keypress', () => this.resetTimer());
    document.addEventListener('click', () => this.resetTimer());
    document.addEventListener('scroll', () => this.resetTimer());
  }

  private initTimer(): void {
    timer(0, 1000)
      .pipe(
        takeUntil(this.destroyed$),
        tap(() => {
          const now = Date.now();
          const timeOut = this.timeoutInSeconds;
          const timeLeft = Math.max(0, timeOut - Math.round((now - this.lastAction) / 1000));
          this.logoutTimer$.next(timeLeft);
        if (timeLeft === 0) {
            this.logout();
          }
        })
      )
      .subscribe();
  }

  private resetTimer(): void {
    this.lastAction = Date.now();
  }

  private logout(): void {
    this.notify.openSnackBar(`Your session will be automatically logged out due to inactivity.`);
    this.router.navigate(['/signin']);
    this.destroyed$.next();
  }

  start(): void {
    this.sharedData.getTimeOutData.subscribe((data) => {
      this.timeoutInSeconds = data;
    });
    this.initTimer();
  }

  get logoutTimer(): Observable<number> {
    return this.logoutTimer$.asObservable();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
